from rest_framework import serializers

from mainapp.models import Campaign, CampaignActivity, Workspace, SpamWord, CampaignContact, CampaignSchedule, UpdateSection


class CampaignSerializer(serializers.ModelSerializer):
    class TimestampField(serializers.Field):
        def to_representation(self, value):
            return value.timestamp() * 1000

    created_on_ts = TimestampField(source="created_on")
    contacts_count = serializers.SerializerMethodField()
    total_emails_sent = serializers.SerializerMethodField()
    total_replies_received = serializers.SerializerMethodField()
    total_leads_contacted = serializers.SerializerMethodField()
    domains = serializers.SerializerMethodField()

    class Meta:
        model = Campaign
        fields = [
            "uid",
            "name",
            "created_on_ts",
            "domains",
            "status",
            "total_emails_sent",
            "total_replies_received",
            "total_leads_contacted",
            "contacts_count",
        ]

    # noinspection PyMethodMayBeStatic
    def get_domains(self, obj: Campaign):
        return [domain.subdomain for domain in obj.sending_domains.all()]

    # noinspection PyMethodMayBeStatic
    def get_contacts_count(self, obj: Campaign) -> int:
        return obj.campaigncontact_set.count()

    # noinspection PyMethodMayBeStatic
    def get_total_emails_sent(self, obj: Campaign) -> int:
        return obj.campaignschedule_set.filter(status="sent").count()

    # noinspection PyMethodMayBeStatic
    def get_total_replies_received(self, obj: Campaign) -> int:
        return obj.campaignschedule_set.filter(status="replied").count()

    # noinspection PyMethodMayBeStatic
    def get_total_leads_contacted(self, obj: Campaign) -> int:
        return obj.campaigncontact_set.values("email_id").distinct().count()


class CampaignActivitySerializer(serializers.ModelSerializer):
    class TimestampField(serializers.Field):
        def to_representation(self, value):
            return value.timestamp() * 1000

    event_date_ts = TimestampField(source="event_date")
    campaign_schedule_uid = serializers.SerializerMethodField()
    
    class Meta:
        model = CampaignActivity
        fields = [
            "id",
            "event_type",
            "event_date_ts",
            "event_subject",
            "event_from",
            "event_additional_data",
            "campaign_schedule_uid",
        ]

    def get_campaign_schedule_uid(self, obj):
        """
        DRF will call this to populate `campaign_schedule_uid`.
        """
        email_s3_key = obj.event_additional_data.get("email_s3_key")
        if not email_s3_key:
            return None
        try:
            schedule = CampaignSchedule.objects.get(email_s3_key=email_s3_key)
            return schedule.uid
        except CampaignSchedule.DoesNotExist:
            return None

class WorkspaceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Workspace
        fields = [
            "id",
            "name"
        ]


class SpamWordsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SpamWord
        fields = [
            "id",
            "name",
        ]


class SalutationWordsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SpamWord
        fields = [
            "id",
            "name",
        ]

class CampaignExportContactSerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField()
    sent_on = serializers.SerializerMethodField()
    reply = serializers.SerializerMethodField()
    bad_email = serializers.SerializerMethodField()
    unsubscribed = serializers.SerializerMethodField()
    reason = serializers.SerializerMethodField()
    attributes = serializers.JSONField()

    class Meta:
        model = CampaignContact
        fields = [
            "email_id", "status", "sent_on", "reply", "bad_email", "unsubscribed", "reason", "attributes"
        ]

    def get_status(self, obj):
        schedule = self.context["schedule_map"].get(obj.id)
        return schedule.status if schedule else "Not Scheduled"

    def get_sent_on(self, obj):
        schedule = self.context["schedule_map"].get(obj.id)
        return schedule.sent_on.strftime("%Y-%m-%d %H:%M") if schedule and schedule.sent_on else ""

    def get_reply(self, obj):
        schedule = self.context["schedule_map"].get(obj.id)
        return schedule.reply_classification if schedule else ""

    def get_bad_email(self, obj):
        return "Yes" if obj.email_id in self.context["bad_emails"] else "No"

    def get_unsubscribed(self, obj):
        return "Yes" if obj.email_id in self.context["unsubscribed"] else "No"

    def get_reason(self, obj):
        if obj.email_id in self.context["bad_emails"]:
            return self.context["bad_reason_map"].get(obj.email_id, "")
        return ""
    
class CreditHistorySerializer(serializers.ModelSerializer):
    class TimestampField(serializers.Field):
        def to_representation(self, value):
            return value.timestamp() * 1000
    
    uid = serializers.CharField(source="campaign.uid")
    campaign_name = serializers.CharField(source="campaign.name")
    receiver = serializers.CharField(source="contact.email_id")
    sender = serializers.SerializerMethodField()
    time_of_email_sent = TimestampField(source="sent_on")
    status = serializers.SerializerMethodField()

    class Meta:
        model = CampaignSchedule
        fields = ["uid", "campaign_name", "receiver", "sender", "time_of_email_sent", "status"]

    def get_sender(self, obj):
        return obj.contact.sending_email.email_address if obj.contact.sending_email else None

    def get_status(self, obj):
        
        latest_activity = (
            CampaignActivity.objects
            .filter(campaign=obj.campaign, event_from=obj.contact.email_id)
            .order_by("-event_date")
            .first()
        )

        if latest_activity:
            if latest_activity.event_type == "unsubscribe":
                return "Unsubscribe"
            elif latest_activity.event_type == "resubscribe":
                return "Resubscribe"

        if obj.status == "failed":
            return "Failed"
        elif obj.status == "replied":
            if obj.reply_classification == "positive":
                return "Positive"
            elif obj.reply_classification == "negative":
                return "Negative"
            elif obj.reply_classification == "neutral":
                return "Neutral"
            else:
                return "Replied"
        elif obj.status == "sent":          
                return "Success"
        elif obj.status == "cancelled_unsubscribed":
            if obj.reply_classification == "negative":
                return "Negative"
            return "Unsubscribed"
        elif obj.status == "cancelled_bad_email":
            return "Bounced"
        else:
            return obj.status.title()


class UpdateSectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = UpdateSection
        fields = ["id", "title", "description", "created_at"]
    
        
class AdminCampaignSerializer(serializers.ModelSerializer):
    user_email = serializers.EmailField(source="workspace.user.email", read_only=True)
    campaign_id = serializers.CharField(source="uid", read_only=True)
    campaign_name = serializers.CharField(source="name", read_only=True)
    user_id = serializers.IntegerField(source="workspace.user.id", read_only=True)
    class Meta:
        model = Campaign
        fields = [
            "campaign_id",
            "campaign_name",
            "user_id",
            "user_email",
            "status",
            "archived",
        ]