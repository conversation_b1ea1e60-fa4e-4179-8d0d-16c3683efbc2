from mainapp.responses import JsonResponseRedirect


class PlanSelectionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # JWT auth info is only available after view is processed.
        user = request.user

        print(request.path)

        # If an authenticated user has not completed signup plan selection, redirect them.
        if (user.is_authenticated and
                request.method == "GET" and
                not user.signup_plan_selection_done and
                not request.path.startswith('/admin') and
                not request.path.startswith('/auth') and
                not request.path.startswith('/onboarding') and
                not request.path.startswith('/logged-in-user-base-page/') and
                not request.path.startswith('/checkout-success/')):
            return JsonResponseRedirect("/onboarding/plan-selection")

        return response
