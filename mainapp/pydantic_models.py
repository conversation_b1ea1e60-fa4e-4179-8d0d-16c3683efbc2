from typing import List, Dict, Optional, Literal
from datetime import datetime
from pydantic import BaseModel, Field

from mainapp.models import EmailID, CampaignContact


class EmailGeneration(BaseModel):
    usernames: List[str]


class CampaignEmailMessageSchema(BaseModel):
    uid: str
    order: int
    subject: str
    body: str
    next_message_days: int


class SaveCampaignMessageSchema(BaseModel):
    uid: str
    subject: str
    body: str
    content_type: str
    next_message_days: int
    prompt_content: Optional[str] = None
    enable_prompt_processing: bool = False


class GooglePostmasterDomainSchema(BaseModel):
    domain: str
    added_on_ts: float
    txt_value: str


class GooglePostmasterTrafficDataSchema(BaseModel):
    name: str
    userReportedSpamRatio: Optional[float] = Field(default=None)
    ipReputations: List[Dict]
    domainReputation: Optional[Literal["HIGH", "MEDIUM", "LOW", "BAD"]] = Field(default=None)
    spfSuccessRatio: int
    dkimSuccessRatio: int
    dmarcSuccessRatio: float
    inboundEncryptionRatio: int | float


class GetPostmasterDataSchema(BaseModel):
    integration_active: bool
    httpError: bool
    domain_reputation: Optional[Literal["HIGH", "MEDIUM", "LOW", "BAD"]]
    spam_ratio: float = Field(default=0.0)


class Schedule(BaseModel):
    sending_email_id: int  # This is the model object id for EmailID model.
    contact_id: int
    message_id: int
    date: datetime


class EmailContentCheck(BaseModel):
    reject: bool
    reason: str
    trigger_words: List[str]
