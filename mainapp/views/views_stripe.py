import logging
import os
from datetime import datetime
from datetime import timezone as dtz
from typing import Dict

import redis
import stripe
from dateutil.relativedelta import relativedelta
from django.http import HttpResponse
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from stripe import StripeClient, StripeError, Webhook, InvalidRequestError

from ColdEmailerBackend.settings import DEBUG
from mainapp.models import User, Workspace, SubscriptionPlan
from mainapp.responses import JsonResponseBadRequest, JsonResponseSuccess
from mainapp.tasks import send_resume_campaign_email_task, send_email_task
from mainapp.email_messages import new_plan_purchase_email_message

client = StripeClient(os.environ["CE_STRIPE_API_KEY"])

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


@api_view(["POST"])
@permission_classes([AllowAny])
def stripe_webhook(request):
    webhook_secret = os.environ["CE_STRIPE_WEBHOOK_SECRET"]
    payload: bytes = request.body
    sig_header: str = request.META['HTTP_STRIPE_SIGNATURE']

    try:
        # thin_event = client.parse_thin_event(payload, sig_header, webhook_secret)
        # event = client.v2.core.events.retrieve(thin_event.id)

        event = Webhook.construct_event(
            payload, sig_header, webhook_secret
        )

        if event.type == "customer.created":
            handle_customer_created(event)

        elif event.type == "customer.deleted":
            handle_customer_deleted(event)

        elif event.type == "customer.subscription.created":
            handle_customer_subscription_created(event)

        elif event.type == "customer.subscription.updated":
            handle_customer_subscription_updated(event)

        elif event.type == 'customer.subscription.deleted':
            handle_customer_subscription_deleted(event)

        elif event.type == 'invoice.paid':
            handle_invoice_paid(event)

        elif event.type == 'invoice.payment_failed':
            handle_invoice_payment_failed(event)

        else:
            print(f"Unhandled event type: {event.type}")

        return HttpResponse(status=200)

    except StripeError as e:
        print(f"Error processing webhook: {str(e)}")
        return HttpResponse(status=400)


# ================================================================
# ----------------------- CUSTOMER CREATED -----------------------
# ================================================================

def handle_customer_created(event):
    """
    Saves stripe customer id to User model based on provided email id.
    """
    logger.debug("Stripe customer created event fired.")
    email: str = event["data"]["object"]["email"]
    if not email:
        logger.error("Email missing in Stripe 'customer.created' webhook event.")
        return

    try:
        user: User = User.objects.get(email=email)
    except User.DoesNotExist:
        logger.error(f"Could not find user {email} for Stripe 'customer.created' webhook event.")
        return

    user.stripe_customer_id = event["data"]["object"]["id"]
    user.save()

    logger.info(f"Stripe customer id saved for user {email}.")


# ================================================================
# ----------------------- CUSTOMER DELETED -----------------------
# ================================================================

def handle_customer_deleted(event):
    """
    Deletes stripe customer id from User model.
    """
    logger.debug("Stripe customer deleted event fired.")
    customer_id: str = event["data"]["object"]["id"]

    try:
        user: User = User.objects.get(stripe_customer_id=customer_id)
    except User.DoesNotExist:
        logger.error(f"Could not find user with stripe customer id {customer_id} for 'customer.deleted' event.")
        return

    user.stripe_customer_id = None
    user.save()

    logger.info(f"Removed stripe customer id for user {user.email}")


# ================================================================
# --------------------- SUBSCRIPTION CREATED ---------------------
# ================================================================

def handle_customer_subscription_created(event):
    """
    Moves workspace to new plan (free to paid).

    No need to check for duplicate event since we are only updating the details here. No credits are being added.
    """
    logger.debug("Stripe customer subscription crated event fired.")

    workspace_id: str = event["data"]["object"]["metadata"]["workspace_id"]
    subscription_id: str = event["data"]["object"]["id"]

    workspace: Workspace = Workspace.objects.get(id=workspace_id)

    # Fetch the plan.
    product_id: str = event["data"]["object"]["items"]["data"][0]["plan"]["product"]
    interval: str = event["data"]["object"]["items"]["data"][0]["plan"]["interval"]
    plan = SubscriptionPlan.objects.get(product_id=product_id)
    next_renewal = datetime.fromtimestamp(
        event["data"]["object"]["items"]["data"][0]["current_period_end"]
    ).replace(tzinfo=dtz.utc)

    # Update workspace.
    workspace.subscription_plan = plan
    workspace.stripe_subscription_id = subscription_id
    workspace.billing_period = "monthly" if interval == "month" else "annual"
    workspace.next_renewal_date = next_renewal

    workspace.save()

    # Update user object.
    workspace.user.is_new_user_discount_eligible = False
    workspace.user.is_trial_period_eligible = False
    workspace.user.signup_plan_selection_done = True
    # workspace.user.email_verified = True
    workspace.user.save()

    send_email_task.delay(
        to=os.environ["CE_NEW_SIGNUP_ALERT_EMAILS"].split(","),
        sender="<EMAIL>",
        sender_name="Deliveryman",
        subject="Checkout completed on DeliverymanAI",
        body_html= new_plan_purchase_email_message(username=workspace.user.username, 
                                                   email=workspace.user.email, 
                                                   plan_name=plan.plan_name, 
                                                   )
    )

    logger.info(f"New subscription created for workspace {workspace.name} (id: {workspace.id})")


# ================================================================
# --------------------- SUBSCRIPTION UPDATED ---------------------
# ================================================================

def handle_customer_subscription_updated(event):
    """
    Moves workspace to new plan (paid to paid).

    No need to check for duplicate event since we are only updating the details here. No credits are being added.
    """
    logger.debug("Stripe customer subscription updated event fired.")

    # Check if this is a pending update. In this case we'll ignore it.
    pending_update: Dict | None = event["data"]["object"]["pending_update"]
    if pending_update is not None:
        logger.info("Received a pending update for 'customer.subscription.updated' event. Ignoring.")
        return

    subscription_id: str = event["data"]["object"]["id"]

    # Fetch the workspace.
    workspace_id: str = event["data"]["object"]["metadata"]["workspace_id"]
    workspace = Workspace.objects.get(id=workspace_id)
    old_plan_name: str = workspace.subscription_plan.plan_name
    old_plan_billing_period: str = workspace.billing_period

    # Update the subscription details on workspace.
    product_id: str = event["data"]["object"]["items"]["data"][0]["plan"]["product"]
    interval: str = event["data"]["object"]["items"]["data"][0]["plan"]["interval"]
    new_plan = SubscriptionPlan.objects.get(product_id=product_id)
    next_renewal = datetime.fromtimestamp(
        event["data"]["object"]["items"]["data"][0]["current_period_end"]
    ).replace(tzinfo=dtz.utc)

    workspace.subscription_plan = new_plan
    workspace.stripe_subscription_id = subscription_id
    workspace.billing_period = "monthly" if interval == "month" else "annual"
    workspace.next_renewal_date = next_renewal

    workspace.save()

    billing_period = "monthly" if interval == "month" else "annual"
    logger.info(f"Subscription updated for workspace {workspace.name} (id: {workspace.id}) - "
                f"Old plan: {old_plan_name} ({old_plan_billing_period}) - "
                f"New Plan: {new_plan.plan_name} ({billing_period})")


# ====================================================================
# ----------------------- SUBSCRIPTION DELETED -----------------------
# ====================================================================

def handle_customer_subscription_deleted(event):
    """
    Moves workspace to free plan and removes subscription id.
    """
    logger.debug("Stripe customer subscription deleted event fired.")

    # Fetch the workspace.
    subscription_id: str = event["data"]["object"]["id"]
    workspace: Workspace = Workspace.objects.get(stripe_subscription_id=subscription_id)

    # Fetch the free plan.
    try:
        free_plan = SubscriptionPlan.objects.get(is_free_plan=True)
    except SubscriptionPlan.DoesNotExist:
        raise Exception("Failed to fetch free plan in 'customer.subscription.deleted' event. "
                        "Make sure 'is_free_plan' field is set.")

    # Update workspace.
    workspace.subscription_plan = free_plan
    workspace.billing_period = "monthly"
    workspace.stripe_subscription_id = None
    workspace.billing_period = None
    workspace.credits_remaining = free_plan.monthly_email_sending_quota
    workspace.next_renewal_date = timezone.now() + relativedelta(months=+1)
    workspace.save()

    logger.info(f"Subscription deleted and moved to free plan for workspace {workspace.name} (id: {workspace.id})")


# ================================================================
# ------------------------- INVOICE PAID -------------------------
# ================================================================

def handle_invoice_paid(event):
    """
    We'll use this to add credits.
    """
    logger.debug("Stripe invoice paid event fired.")

    event_id: str = event["id"]

    with redis.Redis(
            host=os.environ["CE_REDIS_HOST"],
            port=int(os.environ["CE_REDIS_PORT"]),
            db=int(os.environ["CE_REDIS_DB"])
    ) as redis_client:
        is_processed = redis_client.exists(f"stripe:webhook:{event_id}")

        # Check if this is a duplicate event.
        if not is_processed:
            billing_reason: str = event["data"]["object"]["billing_reason"]
            metadata: Dict = event["data"]["object"]["lines"]["data"][0]["metadata"]
            subscription_id: str = event["data"]["object"]["lines"]["data"][0]["parent"]["subscription_item_details"][
                "subscription"]

            # We'll only handle these reasons:
            if billing_reason in ["subscription_create", "subscription_update", "subscription_cycle"]:
                subscription_data: Dict = stripe.Subscription.retrieve(subscription_id)
                product_id: str = subscription_data["items"]["data"][0]["plan"]["product"]
                interval: str = subscription_data["items"]["data"][0]["plan"]["interval"]

                # Fetch the subscription plan data from database.
                plan = SubscriptionPlan.objects.get(product_id=product_id)

                # Fetch workspace using metadata or subscription id. Priority given to workspace id since subscription
                # id might not be set on workspace object for new subscriptions.
                if "workspace_id" in metadata:
                    logger.debug("Used workspace id")
                    workspace = Workspace.objects.get(id=metadata["workspace_id"])
                else:
                    logger.debug("Used subscription id")
                    workspace = Workspace.objects.get(stripe_subscription_id=subscription_id)

                # Add credits based on billing period.
                if interval == "month":
                    workspace.credits_remaining += plan.monthly_email_sending_quota
                else:
                    workspace.credits_remaining += plan.annual_email_sending_quota

                workspace.save()

                # Update user object.
                workspace.user.has_purchased_plan = True
                workspace.user.save()

                # If there are paused campaigns less than 60 days old, then send an email.
                send_resume_campaign_email_task.delay(workspace.id)

                logger.info(f"Added {plan.monthly_email_sending_quota} additional credits to "
                            f"Workspace '{workspace.name}' (id: {workspace.id})")

            else:
                logger.debug(
                    f"Received 'invoice.paid' event with unhandled billing reason '{billing_reason}'. Skipping.")

            # Store event id in redis to avoid duplicate events. Expires in 48 hrs.
            redis_client.setex(f"stripe:webhook:{event_id}", 48 * 60 * 60, 1)

        else:
            logger.info(f"Received duplicate 'invoice.paid' event with id {event_id}. Skipping.")


# ================================================================
# ------------------------ PAYMENT FAILED ------------------------
# ================================================================

def handle_invoice_payment_failed(event):
    logger.debug("Stripe invoice payment failed event fired.")
    logger.debug(event)


# ================================================================
# -------------------------- OTHER APIS --------------------------
# ================================================================

# noinspection PyUnboundLocalVariable
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def stripe_checkout_success(request):
    """
    API for Stripe checkout success page.
    """
    user: User = request.user

    try:
        session_id: str = request.query_params["session_id"]
    except KeyError as k:
        logger.critical(f"stripe_checkout_success() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    try:
        session_data: Dict = stripe.checkout.Session.retrieve(session_id, expand=[
            "line_items"
        ])
    except InvalidRequestError as err:
        logger.error(f"{err}")
        return JsonResponseBadRequest(data={
            "message": "Could not process request. Please try again later."
        })

    # Check if data belongs to user.
    customer_id: str = session_data["customer"]
    if user.stripe_customer_id != customer_id:
        logger.error("Stripe data does not belong to this user.")
        return JsonResponseBadRequest(data={
            "message": "Invalid checkout session data requested."
        })

    product_id: str = session_data["line_items"]["data"][0]["price"]["product"]
    interval: str = session_data["line_items"]["data"][0]["price"]["recurring"]["interval"]
    currency: str = session_data["currency"]
    amount_total: int = session_data["amount_total"]
    # next_billing_ts: int = int(session_data["subscription"]["items"]["data"][0]["current_period_end"] * 1000)
    # invoice_pdf_link: str = session_data["invoice"]["invoice_pdf"]

    plan = SubscriptionPlan.objects.get(product_id=product_id)

    return JsonResponseSuccess(data={
        "plan_name": plan.plan_name,
        "billing_period": "Monthly" if interval == "month" else "Annual",
        "amount_total": amount_total,
        "currency": currency,
        # "invoice_pdf_link": invoice_pdf_link,
        "credits": plan.monthly_email_sending_quota if interval == "month" else plan.annual_email_sending_quota,
        # "next_billing_ts": next_billing_ts,
        "feature_list": plan.monthly_feature_list if interval == "month" else plan.annual_feature_list,
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def stripe_plan_update_success(request):
    """
    API for Stripe plan update success page.
    """
    user: User = request.user

    try:
        sub_id: str = request.query_params["sub_id"]
    except KeyError as k:
        logger.critical(f"stripe_checkout_success() - Missing key {k}")
        return JsonResponseBadRequest(data={
            "message": f"Missing key {k}"
        })

    # Check if data belongs to workspace.
    if user.active_workspace.stripe_subscription_id != sub_id:
        return JsonResponseBadRequest(data={
            "message": "Invalid checkout subscription data requested."
        })

    try:
        subscription_data: Dict = stripe.Subscription.retrieve(sub_id, expand=["latest_invoice"])
    except InvalidRequestError as err:
        logger.error(f"{err}")
        return JsonResponseBadRequest(data={
            "message": "Could not process request. Please try again later."
        })

    product_id: str = subscription_data["items"]["data"][0]["plan"]["product"]
    interval: str = subscription_data["items"]["data"][0]["plan"]["interval"]
    currency: str = subscription_data["items"]["data"][0]["plan"]["currency"]
    # next_billing_ts: int = int(subscription_data["items"]["data"][0]["current_period_end"] * 1000)
    invoice_pdf_link: str = subscription_data["latest_invoice"]["invoice_pdf"]

    plan = SubscriptionPlan.objects.get(product_id=product_id)

    return JsonResponseSuccess(data={
        "plan_name": plan.plan_name,
        "billing_period": "Monthly" if interval == "month" else "Annual",
        "currency": currency,
        "invoice_pdf_link": invoice_pdf_link,
        "credits": plan.monthly_email_sending_quota if interval == "month" else plan.annual_email_sending_quota,
        # "next_billing_ts": next_billing_ts,
        "feature_list": plan.monthly_feature_list if interval == "month" else plan.annual_feature_list,
    })
