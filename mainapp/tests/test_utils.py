from unittest import TestCase
from unittest.mock import patch, MagicMock

from mainapp.utils import csv_rows_to_dict, process_prompt_content


class UtilsTest(TestCase):
    def test_csv_rows_to_dict(self):
        headers = ["key1", "key2", "key3"]
        data = [
            ["value11", "value12", "value13"],
            ["value21", "value22", "value23"],
            ["value31", "value32", "value33"],
        ]
        result = csv_rows_to_dict(data, headers)
        self.assertListEqual(result, [
            {"key1": "value11", "key2": "value12", "key3": "value13"},
            {"key1": "value21", "key2": "value22", "key3": "value23"},
            {"key1": "value31", "key2": "value32", "key3": "value33"},
        ])

    @patch('mainapp.utils.prompt_openai_client')
    def test_process_prompt_content_success(self, mock_client):
        """Test successful prompt processing"""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices[0].message.content = "Generated personalized content"
        mock_client.chat.completions.create.return_value = mock_response

        # Test data
        prompt = "Write a greeting for {{name}} who works at {{company}}"
        contact_attributes = {
            "name": "John Doe",
            "company": "TechCorp",
            "designation": "Manager"
        }

        # Call function
        result = process_prompt_content(prompt, contact_attributes)

        # Assertions
        self.assertEqual(result, "Generated personalized content")
        mock_client.chat.completions.create.assert_called_once()

        # Check that the call included the contact information
        call_args = mock_client.chat.completions.create.call_args
        user_message = call_args[1]['messages'][1]['content']
        self.assertIn("name: John Doe", user_message)
        self.assertIn("company: TechCorp", user_message)
        self.assertIn("designation: Manager", user_message)
        self.assertIn(prompt, user_message)

    def test_process_prompt_content_empty_prompt(self):
        """Test handling of empty prompt"""
        result = process_prompt_content("", {"name": "John"})
        self.assertEqual(result, "")

        result = process_prompt_content(None, {"name": "John"})
        self.assertEqual(result, "")

    @patch('mainapp.utils.prompt_openai_client')
    def test_process_prompt_content_api_error(self, mock_client):
        """Test handling of API errors"""
        # Mock API error
        mock_client.chat.completions.create.side_effect = Exception("API Error")

        prompt = "Test prompt"
        contact_attributes = {"name": "John"}

        # Should return original prompt on error
        result = process_prompt_content(prompt, contact_attributes)
        self.assertEqual(result, prompt)

    def test_process_prompt_content_empty_attributes(self):
        """Test handling of empty contact attributes"""
        with patch('mainapp.utils.prompt_openai_client') as mock_client:
            mock_response = MagicMock()
            mock_response.choices[0].message.content = "Generated content"
            mock_client.chat.completions.create.return_value = mock_response

            result = process_prompt_content("Test prompt", {})

            # Should still work with empty attributes
            self.assertEqual(result, "Generated content")

            # Check that "No additional context" was used
            call_args = mock_client.chat.completions.create.call_args
            user_message = call_args[1]['messages'][1]['content']
            self.assertIn("No additional context available", user_message)
